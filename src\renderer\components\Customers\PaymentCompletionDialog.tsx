import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Box,
  Typography,
  Alert,
  CircularProgress
} from '@mui/material';
import { DateTimePicker } from '@mui/x-date-pickers/DateTimePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { ptBR } from 'date-fns/locale';
import { format } from 'date-fns';
import { CustomerPackage } from '../../types/packages';

interface PaymentCompletionDialogProps {
  open: boolean;
  onClose: () => void;
  onConfirm: (paidAt: Date) => Promise<void>;
  customerPackage: CustomerPackage | null;
  loading?: boolean;
}

export const PaymentCompletionDialog: React.FC<PaymentCompletionDialogProps> = ({
  open,
  onClose,
  onConfirm,
  customerPackage,
  loading = false
}) => {
  const [paidAt, setPaidAt] = useState<Date | null>(new Date());
  const [error, setError] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    if (open) {
      setPaidAt(new Date());
      setError(null);
    }
  }, [open]);

  const handleConfirm = async () => {
    if (!paidAt) {
      setError('Por favor, selecione a data e hora do pagamento.');
      return;
    }

    if (paidAt > new Date()) {
      setError('A data do pagamento não pode ser no futuro.');
      return;
    }

    try {
      setIsSubmitting(true);
      setError(null);
      await onConfirm(paidAt);
      onClose();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Erro ao completar pagamento');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    if (!isSubmitting) {
      onClose();
    }
  };

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={ptBR}>
      <Dialog 
        open={open} 
        onClose={handleClose}
        maxWidth="sm"
        fullWidth
        disableEscapeKeyDown={isSubmitting}
      >
        <DialogTitle>
          Completar Pagamento
        </DialogTitle>
        
        <DialogContent>
          {customerPackage && (
            <Box sx={{ mb: 3 }}>
              <Typography variant="body1" gutterBottom>
                <strong>Pacote:</strong> {customerPackage.package?.name || 'N/A'}
              </Typography>
              <Typography variant="body1" gutterBottom>
                <strong>Valor:</strong> R$ {customerPackage.price_paid?.toFixed(2) || '0,00'}
              </Typography>
              <Typography variant="body1" gutterBottom>
                <strong>Data de Compra:</strong> {' '}
                {format(new Date(customerPackage.purchase_date), 'dd/MM/yyyy HH:mm', { locale: ptBR })}
              </Typography>
            </Box>
          )}

          {error && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
            </Alert>
          )}

          <Box sx={{ mt: 2 }}>
            <DateTimePicker
              label="Data e Hora do Pagamento"
              value={paidAt}
              onChange={(newValue) => setPaidAt(newValue)}
              format="dd/MM/yyyy HH:mm"
              ampm={false}
              slotProps={{
                textField: {
                  fullWidth: true,
                  required: true,
                  error: !!error && !paidAt,
                  helperText: !paidAt ? 'Campo obrigatório' : ''
                }
              }}
              maxDateTime={new Date()}
            />
          </Box>

          <Typography variant="body2" color="text.secondary" sx={{ mt: 2 }}>
            Esta data será usada para calcular as estatísticas de receita nos relatórios.
          </Typography>
        </DialogContent>

        <DialogActions>
          <Button 
            onClick={handleClose}
            disabled={isSubmitting}
          >
            Cancelar
          </Button>
          <Button 
            onClick={handleConfirm}
            variant="contained"
            disabled={isSubmitting || !paidAt}
            startIcon={isSubmitting ? <CircularProgress size={16} /> : null}
          >
            {isSubmitting ? 'Processando...' : 'Confirmar Pagamento'}
          </Button>
        </DialogActions>
      </Dialog>
    </LocalizationProvider>
  );
};
